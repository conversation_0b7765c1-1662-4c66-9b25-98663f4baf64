<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * TODO describe file locallib
 *
 * @package    local_sgeintegracao
 * @copyright  2024 RAPHAEL ENES <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

use core_customfield\handler;
use core_customfield\data_controller;
use core_customfield\field_controller;

require_once($CFG->dirroot . '/local/sgeintegracao/lib.php');


/**
 * Envia os dados de avaliação para o sistema SGE.
 *
 * @param string $CodProva Código da prova.
 * @param string $IdTurmaDisc ID da turma ou disciplina.
 * @param string $Descricao Descrição da avaliação.
 * @param float $Valor Valor máximo da avaliação.
 * @param float $Media Média necessária para aprovação.
 * @return string Resposta da API SGE.
 */
function send_assessment_data_sge($CodProva, $IdTurmaDisc, $Descricao, $Valor, $Media)
{

    $avaliacao = get_config('local_sgeintegracao', 'avaliacao');
    $authorization_sge = get_config('local_sgeintegracao', 'authorization_sge');
    $codcoligada = get_config('local_sgeintegracao', 'codcoligada');

    $curl = curl_init();

    // Prepara os dados para enviar
    $postData = json_encode(array(
        "CodProva" => (string) $CodProva,
        "IdTurmaDisc" => (string) $IdTurmaDisc,
        "Descricao" => (string) $Descricao,
        "Valor" => intval($Valor),
        "DataPrevista" => "",
        "Media" => intval($Media),
        "CodColigada" => (string) $codcoligada,
        "LMSouCTM" => 0
    ));

    curl_setopt_array($curl, array(
        CURLOPT_URL => $avaliacao,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'PUT',
        CURLOPT_POSTFIELDS => $postData,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: ' . 'Basic ' . $authorization_sge
        ),
    ));

    $response = curl_exec($curl);

    curl_close($curl);

    return $response;
}

/**
 * Envia a nota de cada atividade o sistema SGE.
 *
 * @param string $CodProva Código da prova.
 * @param float $Nota Nota obtida pelo aluno.
 * @param string $RA Registro Acadêmico (RA) do aluno.
 * @param string $IdTurmaDisc ID da turma ou disciplina.
 * @return string Resposta da API SGE.
 */
function send_grade_data($CodProva, $Nota, $RA, $IdTurmaDisc)
{

    $etapa_avaliacao = get_config('local_sgeintegracao', 'etapa_avaliacao');
    $authorization_sge = get_config('local_sgeintegracao', 'authorization_sge');
    $codcoligada = get_config('local_sgeintegracao', 'codcoligada');

    $curl = curl_init();

    $postData = json_encode(array(
        "CodProva" => $CodProva,
        "Nota" => $Nota,
        "RA" => $RA,
        "CodColigada" => $codcoligada,
        "IdTurmaDisc" => $IdTurmaDisc,
        "LMSouCTM" => 0
    ));

    curl_setopt_array($curl, array(
        CURLOPT_URL => $etapa_avaliacao,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'PUT',
        CURLOPT_POSTFIELDS => $postData,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: ' . 'Basic ' . $authorization_sge
        ),
    ));

    $response = curl_exec($curl);

    curl_close($curl);

    return $response;
}

function get_data_sge($idturmadisc)
{
    $executa_consultasql = get_config('local_sgeintegracao', 'executa_consultasql');
    $authorization_sge = get_config('local_sgeintegracao', 'authorization_sge');
    $codcoligada = get_config('local_sgeintegracao', 'codcoligada');

    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => $executa_consultasql . '?codcoligada=' . $codcoligada . '&codsentenca=MOODLE.0006&parameters=CODCOLIGADA%3D' . $codcoligada . '%3BIDTURMADISC%3D' . $idturmadisc . '&codsistema=S',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: ' . 'Basic ' . $authorization_sge
        ),
    ));

    $response = curl_exec($curl);

    curl_close($curl);

    $data = json_decode($response, true);

    $maxCodProva = null;

    foreach ($data['Row'] as $row) {
        if ($maxCodProva === null || $row['CODPROVA'] > $maxCodProva) {
            $maxCodProva = $row['CODPROVA'];
        }
    }

    return $maxCodProva;

}

function get_course_grade_items($courseid)
{
    global $DB;

    $sql = "SELECT gi.id AS itemid, gi.idnumber, gi.itemname, gi.grademax, gi.gradepass, c.idnumber AS courseidnumber, c.id AS courseid
            FROM {grade_items} gi
            JOIN {course} c ON gi.courseid = c.id
            WHERE gi.courseid = :courseid
            AND gi.itemtype != 'course'
            AND gi.hidden = 0
            ORDER BY gi.itemname";

    // Executa a query com o courseid passado como parâmetro
    $grades = $DB->get_recordset_sql($sql, ['courseid' => $courseid]);

    $grades_array = [];
    foreach ($grades as $record) {
        $grades_array[] = $record;
    }
    return $grades_array;
}

function progress_itens_sge($gradesitens, $courseid)
{
    global $DB;

    // Obtém o idnumber do curso
    $idturmadisc = $DB->get_field('course', 'idnumber', array('id' => $courseid), IGNORE_MISSING);

    // Exibe o título acima da barra de progresso
    echo "<h3>Enviando Atividades para Integração</h3>";

    $total = count($gradesitens);
    $progress = new \core\progress\display();

    $progress->start_progress('Enviando atividades para Interação...', $total);

    // Inicializa a tabela
    echo "<table class='generaltable'>";
    echo "<thead><tr><th>Nome Atividade</th><th>Retorno SGE</th></tr></thead>";
    echo "<tbody>";

    $y = 1;
    $i = null;

    foreach ($gradesitens as $itemid => $grade) {
        $courseidnumber = $grade->courseidnumber;
        $itemname = $grade->itemname;
        $grademax = (int) $grade->grademax;
        $gradepass = (int) $grade->gradepass;
        $codprova_dr = $grade->idnumber;

        // Gera ou utiliza o CODPROVA
        if (empty($codprova_dr)) {
            $maxcodprova = get_data_sge($idturmadisc);
            $i = $maxcodprova + 1;
            $codprova = 'A' . (string) $i . 'E1';
        } else {
            $codprova = $codprova_dr;
        }

        // Envia os dados para o SGE
        $response = send_assessment_data_sge($codprova, $courseidnumber, $itemname, $grademax, $gradepass);
        $responseData = json_decode($response, true);

        // Captura a mensagem de retorno do SGE
        $mensagem = isset($responseData['Mensagem']) ? s($responseData['Mensagem']) : 'Nenhuma mensagem recebida';

        if (strcasecmp($mensagem, 'Avaliação Criada/Atualizada com Sucesso.') === 0) {
            // Atualiza o idnumber no grade_items
            $DB->set_field('grade_items', 'idnumber', $codprova, array('id' => $grade->itemid));
        }

        // Verifica se o registro já existe na tabela
        $existingRecord = $DB->get_record('local_sgeintegracao_atv', array('idcurso' => $grade->courseid, 'idatv' => $grade->itemid), '*', IGNORE_MISSING);

        if ($existingRecord) {
            // Se o registro existir
            $existingRecord->codatividade = $codprova;
            $existingRecord->nomeatividade = $itemname;
            $existingRecord->retornosge = $mensagem;
            $DB->update_record('local_sgeintegracao_atv', $existingRecord);
        } else {
            // Se o registro não existir
            $record = new stdClass();
            $record->idcurso = $grade->courseid;
            $record->idatv = $grade->itemid;
            $record->codatividade = $codprova;
            $record->nomeatividade = $itemname;
            $record->retornosge = $mensagem;
            $DB->insert_record('local_sgeintegracao_atv', $record);
        }

        $rowclass = (strcasecmp($mensagem, 'Avaliação Criada/Atualizada com Sucesso.') === 0) ? 'class="success-row"' : 'class="error-row"';

        // Exibe a linha da tabela
        echo "<tr $rowclass><td>" . s($itemname) . "</td><td>" . s($mensagem) . "</td></tr>";

        // Atualiza o progresso
        $progress->progress($y);
        $y++;
    }

    echo "</tbody>";
    echo "</table>";

    $progress->end_progress();

}

function get_course_grades_users($courseid)
{
    global $DB;

    $sql = "SELECT gi.id AS itemid, gi.itemname, gi.grademax, gi.gradepass, gg.userid, gg.finalgrade,
            u.id AS userid, u.firstname, u.lastname, u.idnumber AS matricula,
            c.idnumber AS courseidnumber, c.id AS courseid,
            gi.idnumber AS idnumber
            FROM {grade_items} gi
            JOIN {grade_grades} gg ON gi.id = gg.itemid
            JOIN {user} u ON gg.userid = u.id
            JOIN {course} c ON gi.courseid = c.id
            WHERE gi.courseid = :courseid
            AND gi.itemtype != 'course'
            AND gg.finalgrade is not null
            ORDER BY u.lastname, u.firstname";

    // Executa a query com o courseid passado como parâmetro
    $grades = $DB->get_recordset_sql($sql, ['courseid' => $courseid]);

    $grades_array = [];

    foreach ($grades as $record) {
        $grades_array[] = $record;
    }
    return $grades_array;

}



function progress_grader_sge($gradesitens)
{
    global $DB;

    $total = count($gradesitens);
    $i = 1;

    // Exibe o título acima da barra de progresso
    echo "<h3>Enviando Usuários para Integração</h3>";

    $progress = new \core\progress\display();

    // Inicia o progresso
    $progress->start_progress('Enviando avaliações...', $total);

    // Inicializa a tabela
    echo "<table class='generaltable'>";
    echo "<thead><tr><th>Nome Completo</th><th>Atividade</th><th>Nota</th><th>Retorno SGE</th></tr></thead>";
    echo "<tbody>";

    foreach ($gradesitens as $itemid => $grade) {

        $userid = $grade->userid;
        $courseidnumber = $grade->courseidnumber;
        $firstname = $grade->firstname;
        $lastname = $grade->lastname;
        $itemname = $grade->itemname;
        $finalgrade = (int) $grade->finalgrade;
        $codprova = $grade->idnumber;
        $extraprofile = profile_user_record($grade->userid);

        $fullname = $firstname . ' ' . $lastname;

        // Envia os dados da nota para o SGE
        $response = send_grade_data($codprova, $finalgrade, $extraprofile->matricula, $courseidnumber);
        $responseData = json_decode($response, true);

        $mensagem = isset($responseData['Mensagem']) ? s($responseData['Mensagem']) : 'Nenhuma mensagem recebida';

        $existingRecord = $DB->get_record('local_sgeintegracao_notas', array('idcurso' => $grade->courseid, 'idatv' => $grade->itemid, 'iduser' => $userid), '*', IGNORE_MISSING);

        if ($existingRecord) {
            // Se o registro existir
            $existingRecord->codatividade = $codprova;
            $existingRecord->atividade = $itemname;
            $existingRecord->nota = $finalgrade;
            $existingRecord->retornosge = $mensagem;
            $DB->update_record('local_sgeintegracao_notas', $existingRecord);
        } else {
            // Se o registro não existir
            $record = new stdClass();
            $record->iduser = $userid;
            $record->idcurso = $grade->courseid;
            $record->idatv = $grade->itemid;
            $record->codatividade = $codprova;
            $record->nomecompleto = $fullname;
            $record->atividade = $itemname;
            $record->nota = $finalgrade;
            $record->retornosge = $mensagem;
            $DB->insert_record('local_sgeintegracao_notas', $record);
        }

        $rowclass = (strcasecmp($mensagem, 'Importação de Notas em Etapa/Avaliações realizada com Sucesso.') === 0) ? 'class="success-row"' : 'class="error-row"';

        // Exibe a linha da tabela
        echo "<tr $rowclass><td>" . s($fullname) . "</td><td>" . s($itemname) . "</td><td>" . s($finalgrade) . "</td><td>" . s($mensagem) . "</td></tr>";

        // Atualiza o progresso
        $progress->progress($i);

        $i++;
    }

    echo "</tbody>";
    echo "</table>";

    $progress->end_progress();
}

/**
 * Consulta dos cursos e categorias do SGE.
 *
 * @return string Resposta da API SGE de consulta de cursos e categorias.
 */

function sge_course_category()
{

    $courseurlapi = get_config('local_sgeintegracao', 'courseurlapi');
    $authorization_sge_course = get_config('local_sgeintegracao', 'authorization_sge_course');
    $codcoligada_course = get_config('local_sgeintegracao', 'codcoligada_course');
    $codsentenca_course = get_config('local_sgeintegracao', 'codsentenca_course');

    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => $courseurlapi . '?CODCOLIGADA=' . $codcoligada_course . '&CODSENTENCA=' . $codsentenca_course . '&PARAMETERS=CODCOLIGADA%3D' . $codcoligada_course . '&codsistema=S',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Authorization: Basic ' . $authorization_sge_course,

        ),
    ));

    $response = curl_exec($curl);

    curl_close($curl);
    return $response;

}

/**
 * Executa A criação de cursos do SGE.
 *
 */

function sge_course_create()
{

    global $DB, $CFG;


    mtrace(string: "Iniciando a Integração...");

    // Obtém os cursos a partir da função sge_course_category.
    $response = sge_course_category();


    $response = json_decode($response, true);

    $courses = $response['Row'];

    foreach ($courses as $data) {
        if (!is_array($data)) {
            mtrace("Aviso: Um dos itens do array de cursos não é válido.");
            continue;
        }

        // Criar ou obter categoria Modalidade.
        $modalidade = get_or_create_category($data['MODALIDADE'], 0, $data['CODMODALIDADECURSO']);

        // Criar ou obter categoria Área.
        $area = get_or_create_category($data['AREA'], $modalidade->id, $data['CODAREA']);

        // Criar ou obter categoria Curso.
        $curso = get_or_create_category($data['CURSO'], $area->id, $data['CODCURSO']);

        // Criar ou obter categoria Turma.
        $turma = get_or_create_category($data['TURMA'], $curso->id, $data['CODTURMA']);

        if ($DB->record_exists('course', ['shortname' => $data['CODDISC']])) {
            if ($existingcourse = $DB->get_record('course', ['idnumber' => $data['IDTURMADISC']])) {

                $existingcourse->fullname = $data['DISCIPLINA'];
                $existingcourse->category = $turma->id;
                $existingcourse->startdate = !empty($data['INICIO_DISCIPLINA']) ? strtotime($data['INICIO_DISCIPLINA']) : 0;
                $existingcourse->enddate = !empty($data['FIM_DISCIPLINA']) ? strtotime($data['FIM_DISCIPLINA']) : 0;
                $DB->update_record('course', $existingcourse);

                // Atualizações de campo extra de curso
                update_course_custom_field($existingcourse->id, 'modalidade', $data['MODALIDADE']);
                update_course_custom_field($existingcourse->id, 'area', $data['AREA']);
                update_course_custom_field($existingcourse->id, 'curso', $data['CURSO']);
                update_course_custom_field($existingcourse->id, 'turma', $data['TURMA']);
                update_course_custom_field($existingcourse->id, 'unidade', $data['UNIDADE']);

                mtrace("Curso '{$data['CODDISC']}}' já existe, atualizando se necessário");
                continue;
            }
        }

        try {
            $newcourse = new stdClass();
            $newcourse->fullname = $data['DISCIPLINA'];
            $newcourse->shortname = $data['CODDISC'];
            $newcourse->category = $turma->id;
            $newcourse->idnumber = $data['IDTURMADISC'];
            $newcourse->startdate = !empty($data['INICIO_DISCIPLINA']) ? strtotime($data['INICIO_DISCIPLINA']) : 0;
            $newcourse->enddate = !empty($data['FIM_DISCIPLINA']) ? strtotime($data['FIM_DISCIPLINA']) : 0;
            // Verifica se curso já deve ser criado visivel.
            $hoje = time();
            $newcourse->visible = ($newcourse->startdate > 0 && $newcourse->startdate <= $hoje) ? 1 : 0;

            $courseid = create_course($newcourse);
            mtrace("Curso '" . $data['CODDISC'] . "' criado com sucesso (ID: " . $courseid->id . ")");
        } catch (moodle_exception $e) {
            mtrace("Erro ao criar o curso '{$data['CODDISC']}': " . $e->getMessage());
        }
    }

}

/**
 * Cria ou consulta as categorias.
 *
 * @param string $name
 * @param int $parentid
 * @param string $idnumber
 * @return core_course_category
 */
function get_or_create_category($name, $parentid, $idnumber)
{
    global $DB;


    $category = $DB->get_record('course_categories', ['idnumber' => $idnumber]);
    if (!$category) {
        $category = core_course_category::create([
            'name' => $name,
            'parent' => $parentid,
            'idnumber' => $idnumber,
            'visible' => 1
        ]);
        mtrace("Criada categoria: $name (ID: " . $category->id . ")");
    } else {
        $corecategory = core_course_category::get($category->id);
        $corecategory->update([
            'name' => $name,
            'parent' => $parentid,
            'idnumber' => $idnumber
        ]);
        mtrace("Atualizada (se necessário) categoria: $name (ID: " . $category->id . ")");
    }
    return $category;
}

/**
 * Atualiza um campo personalizado de curso.
 * @param int $courseid ID do curso onde o campo será atualizado.
 * @param string $shortname Nome breve do campo personalizado a ser atualizado.
 * @param mixed $value Novo valor a ser atribuído ao campo.
 */

function update_course_custom_field($courseid, $shortname, $value)
{
    global $DB;

    //handler dos campos personalizados de curso.
    $handler = handler::get_handler('core_course', 'course');


    $field = $DB->get_record('customfield_field', [
        'shortname' => $shortname
    ]);


    if (!$field) {
        debugging("Erro: Campo personalizado '{$shortname}' não encontrado.", DEBUG_DEVELOPER);
        return;
    }

    //contexto curso.
    $context = \context_course::instance($courseid);

    //dados existentes do campo.
    $datacontrollers = $handler->get_instance_data($field->id, $courseid);

    $datacontroller = null;

    if (!empty($datacontrollers)) {
        foreach ($datacontrollers as $controller) {
            if ($controller->get_field()->get('shortname') === $shortname && $controller->get('instanceid') == $courseid) {
                $datacontroller = $controller;
                break;
            }
        }
    }

    if ($datacontroller) {
        //tipo correto de armazenamento do campo
        $datafield = $datacontroller->datafield();
        $current_value = $datacontroller->get($datafield);

        //diferente, atualiza o campo
        if ($current_value !== $value) {
            $datacontroller->set($datafield, $value);
            $datacontroller->set('contextid', $context->id);
            $datacontroller->set('timemodified', time());
            $datacontroller->save();
            mtrace("Campo '{$shortname}' atualizado para '{$value}' no curso ID {$courseid}.");
        } else {
            //mtrace("Campo '{$shortname}' já possui o valor '{$value}' no curso ID {$courseid}, sem necessidade de atualização.");
        }
    } else {
        //já existe um registro para esse campo.
        $existing_record = $DB->get_record('customfield_data', [
            'fieldid' => $field->id,
            'instanceid' => $courseid
        ]);

        if ($existing_record) {
            //atualiza campo.
            $datafield = get_datafield_by_type($field->type);
            if ($existing_record->$datafield !== $value) {
                $existing_record->$datafield = $value;
                $existing_record->timemodified = time();
                $DB->update_record('customfield_data', $existing_record);
                mtrace("Campo '{$shortname}' atualizado via banco de dados para '{$value}' no curso ID {$courseid}.");
            } else {
                //mtrace("Campo '{$shortname}' já contém o valor correto no banco de dados. Nenhuma atualização necessária.");
            }
        } else {
            // nova instância de campo extra.
            $datacontroller = data_controller::create(0, null, field_controller::create($field->id));

            // Define os valores corretamente
            $datacontroller->set('contextid', $context->id);
            $datacontroller->set('instanceid', $courseid);
            $datacontroller->set($datacontroller->datafield(), $value);
            $datacontroller->set('timecreated', time());
            $datacontroller->set('timemodified', time());
            $datacontroller->save();

            mtrace("Campo '{$shortname}' criado com valor '{$value}' no curso ID {$courseid}.");
        }
    }
}

/**
 * Retorna o nome da coluna correta no banco de dados para armazenar o valor do campo extra.
 */
function get_datafield_by_type($type)
{
    switch ($type) {
        case 'text':
            return 'charvalue';
        case 'select':
            return 'shortcharvalue';
        case 'int':
            return 'intvalue';
        case 'decimal':
            return 'decvalue';
        default:
            debugging("Erro: Tipo de campo desconhecido '{$type}'.", DEBUG_DEVELOPER);
            return 'value';
    }
}

/**
 * Consulta dos Usuários do SGE.
 *
 * @return string Resposta da API SGE de consulta de usuários.
 */

function sge_user_sge()
{

    $userurlapi = get_config('local_sgeintegracao', 'userurlapi');
    $authorization_sge_user = get_config('local_sgeintegracao', 'authorization_sge_user');
    $codcoligada_user = get_config('local_sgeintegracao', 'codcoligada_user');
    $codsentenca_user = get_config('local_sgeintegracao', 'codsentenca_user');

    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => $userurlapi . '?CODCOLIGADA=' . $codcoligada_user . '&CODSENTENCA=' . $codsentenca_user . '&PARAMETERS=CODCOLIGADA%3D' . $codcoligada_user . '&codsistema=S',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Authorization: Basic ' . $authorization_sge_user,

        ),
    ));

    $response = curl_exec($curl);

    curl_close($curl);
    return $response;

}

/**
 * Executa A criação de usuários do SGE.
 *
 */

function sge_user_create()
{

    global $DB, $CFG;

    require_once($CFG->dirroot . '/user/lib.php');
    require_once($CFG->dirroot . '/user/profile/lib.php');

    mtrace(string: "Iniciando a Integração...");

    // Obtém os usuários a partir da função sge_user_sge.
    $response = sge_user_sge();


    $response = json_decode($response, true);

    $users = $response['Row'];

    foreach ($users as $data) {
        $user = new stdClass();
        $user->username = $data['USUARIO'];
        $user->firstname = $data['NOME'];
        $user->lastname = $data['SOBRENOME'];
        $user->email = $data['EMAIL'];
        $user->auth = 'manual'; // Será preservado para usuários existentes
        $user->city = isset($data['CIDADE']) ? $data['CIDADE'] : '';
        $user->phone1 = isset($data['TELEFONE']) ? $data['TELEFONE'] : '';
        $user->phone2 = isset($data['CELULAR']) ? $data['CELULAR'] : '';
        $user->institution = isset($data['UNIDADE']) ? $data['UNIDADE'] : '';
        $user->idnumber = $data['MATRICULA'];
        $user->confirmed = 1; // Será removido para usuários existentes
        $user->mnethostid = $CFG->mnet_localhost_id; // Será removido para usuários existentes
        $user->dr = $data['DR'];

        $username = trim($data['USUARIO']);

        $existingUser = $DB->get_record('user', ['username' => $username, 'mnethostid' => $CFG->mnet_localhost_id]);

        if ($data['IDCTM'] == NULL) {

            if (!$existingUser) {

                $newuserid = sgeintegracao_createnew_user($user);

                mtrace(string: "Usuário com ID: " . $newuserid);

            } else {
                $userupdate = $DB->get_record('user', ['username' => $user->username, 'mnethostid' => $CFG->mnet_localhost_id]);
                $user->id = $userupdate->id;

                $user->auth = $userupdate->auth;

                unset($user->password);
                unset($user->confirmed);
                unset($user->mnethostid);

                user_update_user($user, false);
                mtrace(string: "Usuário ( ".$user->firstname." ". $user->lastname ." ) alterado com ID: " . $user->id);
            }
        } else {

            $existingUser = sgeintegracao_get_user_CTM($user);

            if (!$existingUser) {

                $newuserid = sgeintegracao_createnew_user_CTM($user);

                if (!$newuserid['message']) {
                    mtrace(string: "Usuário " . $newuserid[0]['username'] . " ( ".$user->firstname." ". $user->lastname ." ) criado no AMB CTM com ID: " . $newuserid[0]['id']);
                } else {
                    mtrace(string: "Erro no AMB CTM: " . $newuserid['message'] . " | DEBUG: " . $newuserid['debuginfo']);
                }

            } else {
                $user->id = $existingUser[0]['id'];
                $userupdate = sgeintegracao_update_user_CTM($user);

                if (!$userupdate['message']) {
                    mtrace('Usuário ( ".$user->firstname." ". $user->lastname ." ) CTM-' . $user->dr . ' | Atualizado com ID Externo: ' . $existingUser[0]['id']);
                } else {
                    mtrace(string: "Erro no AMB CTM: " . $userupdate['message'] . " | DEBUG: " . $userupdate['debuginfo']);
                }
            }

        }
    }
}

/**
 * Consulta dos Matriculas do SGE.
 *
 * @return string Resposta da API SGE de consulta de usuários.
 */

function sge_enrol_sge()
{

    $enrolurlapi = get_config('local_sgeintegracao', 'enrolurlapi');
    $authorization_sge_enrol = get_config('local_sgeintegracao', 'authorization_sge_enrol');
    $codcoligada_enrol = get_config('local_sgeintegracao', 'codcoligada_enrol');
    $codsentenca_enrol = get_config('local_sgeintegracao', 'codsentenca_enrol');

    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => $enrolurlapi . '?CODCOLIGADA=' . $codcoligada_enrol . '&CODSENTENCA=' . $codsentenca_enrol . '&PARAMETERS=CODCOLIGADA%3D' . $codcoligada_enrol . '&codsistema=S',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Authorization: Basic ' . $authorization_sge_enrol,

        ),
    ));

    $response = curl_exec($curl);

    curl_close($curl);
    return $response;

}

/**
 * Executa A criação de matriculas do SGE.
 *
 */

function sge_enrol_create()
{
    global $DB, $CFG;

    require_once($CFG->dirroot . '/enrol/manual/externallib.php');

    mtrace("Iniciando a Integração...");

    // Obtém os dados de matrícula
    $response = sge_enrol_sge();
    $response = json_decode($response, true);

    if (!isset($response['Row']) || empty($response['Row'])) {
        mtrace("Nenhuma matrícula encontrada.");
        return;
    }

    $enrolments = $response['Row'];
    $enrol_manual = new enrol_manual_external();

    foreach ($enrolments as $data) {
        $enrolstatus = $data['SITUACAO'];
        if ($data['IDCTM'] == NULL) {
            // Buscar o ID do curso
            $courseid = $DB->get_field('course', 'id', ['idnumber' => $data['IDTURMADISC']]);

            // Buscar o ID do usuário
            $userid = $DB->get_field('user', 'id', ['username' => $data['USUARIO']]);

            if (!$courseid) {
                mtrace("Erro: Curso com IDTURMADISC '{$data['IDTURMADISC']}' não encontrado.");
                continue;
            }

            if (!$userid) {
                mtrace("Erro: Usuário com matrícula '{$data['USUARIO']}' não encontrado.");
                continue;
            }

            // Verificar a instância de inscrição no curso
            $enrolid = $DB->get_field('enrol', 'id', ['courseid' => $courseid, 'enrol' => 'manual']);

            if (!$enrolid) {
                mtrace("Erro: Método de inscrição manual não encontrado para o curso ID: {$courseid}.");
                continue;
            }

            // Verifica se o usuário já está matriculado
            $existing_enrolment = $DB->get_record('user_enrolments', ['userid' => $userid, 'enrolid' => $enrolid]);

            if ($existing_enrolment) {
                // Usuário já está matriculado, então verificamos se o status precisa ser atualizado
                if ($existing_enrolment->status != $enrolstatus) {
                    $DB->set_field('user_enrolments', 'status', $enrolstatus, ['id' => $existing_enrolment->id]);
                    mtrace("Status da matrícula atualizado para usuário ID: {$userid} no curso ID: {$courseid} (Status: {$enrolstatus}).");
                } else {
                    mtrace("Usuário ID: {$userid} já está matriculado no curso ID: {$courseid}");
                }
                continue;
            }

            // Matricular o usuário no curso
            try {
                $enrol_manual->enrol_users([
                    [
                        'roleid' => 5,
                        'userid' => $userid,
                        'courseid' => $courseid
                    ]
                ]);
                mtrace("Usuário ID: {$userid} matriculado com sucesso no curso ID: {$courseid}.");
            } catch (Exception $e) {
                mtrace("Erro ao matricular usuário ID {$userid} no curso ID {$courseid}: " . $e->getMessage());
            }

        } else {
            $courseid = $data['IDCTM'];
            $userid = sgeintegracao_get_user_CTM($data['USUARIO']);
            $statusenrol = sgeintegracao_is_user_enrolled($userid[0]['id'], $courseid);

            if($statusenrol['message']){
                mtrace('Erro ao consultar API: ' . $enroluser['message'] . '| DEBUG: ' . $enroluser['debuginfo']);
            }

            if ($statusenrol === TRUE) {
                $enroluser = sgeintegracao_enrol_user_CTM($userid[0]['id'], $courseid, $data['SITUACAO'], $data);
                sgeintegracao_group_CTM($userid[0]['id'], $courseid, $data);
                continue;
            } else {
                $enroluser = sgeintegracao_enrol_user_CTM($userid[0]['id'], $courseid, $data['SITUACAO'], $data);
                sgeintegracao_group_CTM($userid[0]['id'], $courseid, $data);

                if (!$enroluser['message']) {
                    mtrace('Usuário CTM com CPF ' . $userid[0]['username'] . ' Cadastrado no Curso AMB CTM ID: ' . $courseid);
                } else {
                    mtrace('Erro ao matricular o Usuário CTM: ' . $enroluser['message'] . '| DEBUG: ' . $enroluser['debuginfo']);
                }

            }
        }
    }
}

