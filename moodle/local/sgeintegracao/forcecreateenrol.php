<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * TODO describe file forcecreatecourse
 *
 * @package    local_sgeintegracao
 * @copyright  2025 Raphael Enes <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
use core\progress\display;;

require('../../config.php');
require_once($CFG->dirroot . '/local/sgeintegracao/locallib.php');
require_once($CFG->dirroot . '/enrol/manual/externallib.php');
require_once($CFG->dirroot . '/local/sgeintegracao/lib.php');

$PAGE->set_url(new moodle_url('/local/sgeintegracao/forcecreateenrol.php'));
$PAGE->set_context(context_system::instance());
$PAGE->set_title(get_string('enrolcreationoutput', 'local_sgeintegracao'));
$PAGE->set_heading(get_string('enrolcreationoutput', 'local_sgeintegracao'));

require_login();
require_capability('moodle/site:configview', context_system::instance());
require_sesskey();

echo $OUTPUT->header();
$settingsurl = new moodle_url('/admin/settings.php', array('section' => 'sgeintegracao_enrolsge_settings'));
$backbutton = html_writer::link($settingsurl, get_string('back', 'core'), array('class' => 'btn btn-secondary'));
echo html_writer::div($backbutton, 'mt-3');

sge_enrol_create_web();

echo $OUTPUT->footer();

function sge_enrol_create_web()
{
    global $DB, $CFG;

    require_once($CFG->dirroot . '/enrol/manual/externallib.php');
    require_once($CFG->dirroot . '/local/sgeintegracao/lib.php'); // caso tenha funções CTM
    require_once($CFG->dirroot . '/local/sgeintegracao/locallib.php'); // se for usado

    mtrace("Iniciando a Integração...");
    flush();

    // Obtém os dados de matrícula
    $response = sge_enrol_sge();
    $response = json_decode($response, true);

    if (!isset($response['Row']) || empty($response['Row'])) {
        mtrace("Nenhuma matrícula encontrada.");
        echo html_writer::tag('pre', "Nenhuma matrícula encontrada.") . "\n";
        flush();
        return;
    }

    $enrolments = $response['Row'];
    $total = count($enrolments);
    $enrol_manual = new enrol_manual_external();


    $progress = new display();
    $progress->start_progress('Processando inscrições', $total);

    $count = 0;
    foreach ($enrolments as $data) {
        $count++;
        $progress->progress($count);

        $enrolstatus = $data['SITUACAO'];
        if ($data['IDCTM'] == NULL) {
            // Verifica se IDMODELO é diferente de NULL
            if (!empty($data['IDMODELO']) && $data['IDMODELO'] !== null) {
                // Busca o curso pelo IDMODELO
                $courseid = $data['IDMODELO'];
                flush();
            } else {
                // Busca o curso pelo IDTURMADISC (comportamento original)
                $courseid = $DB->get_field('course', 'id', ['idnumber' => $data['IDTURMADISC']]);
            }

            $userid = $DB->get_field('user', 'id', ['username' => $data['USUARIO']]);

            if (!$courseid) {
                if (!empty($data['IDMODELO']) && $data['IDMODELO'] !== null) {
                    $msg = "Erro: Curso com IDMODELO '{$data['IDMODELO']}' não encontrado.";
                } else {
                    $msg = "Erro: Curso com IDTURMADISC '{$data['IDTURMADISC']}' não encontrado.";
                }
                echo html_writer::tag('pre', $msg) . "\n";
                flush();
                continue;
            }

            if (!$userid) {
                $msg = "Erro: Usuário com matrícula '{$data['USUARIO']}' não encontrado.";
                echo html_writer::tag('pre', $msg) . "\n";
                flush();
                continue;
            }

            $enrolid = $DB->get_field('enrol', 'id', ['courseid' => $courseid, 'enrol' => 'manual']);

            if (!$enrolid) {
                $msg = "Erro: Método de inscrição manual não encontrado para o curso ID: {$courseid}.";
                echo html_writer::tag('pre', $msg) . "\n";
                flush();
                continue;
            }

            $existing_enrolment = $DB->get_record('user_enrolments', ['userid' => $userid, 'enrolid' => $enrolid]);

            if ($existing_enrolment) {
                if ($existing_enrolment->status != $enrolstatus) {
                    $DB->set_field('user_enrolments', 'status', $enrolstatus, ['id' => $existing_enrolment->id]);
                    $msg = "Status da matrícula atualizado para usuário ID: {$userid} no curso ID: {$courseid} (Status: {$enrolstatus}).";

                    // Se foi por IDMODELO, atualizar também na tabela de controle
                    if (!empty($data['IDMODELO']) && $data['IDMODELO'] !== null) {
                        $status_control = ($enrolstatus == 0) ? 1 : 0; // 1 para matriculado, 0 para suspenso
                        $control_result = sge_insert_matricula_control(
                            $userid,
                            $courseid,
                            $data['IDTURMADISC'],
                            $data['CODFILIAL'],
                            $status_control
                        );

                        if ($control_result) {
                            $msg .= " Registro de controle atualizado.";
                        } else {
                            $msg .= " Erro ao atualizar registro de controle.";
                        }
                    }
                } else {
                    $msg = "Usuário ID: {$userid} já está matriculado no curso ID: {$courseid}";
                }
                echo html_writer::tag('pre', $msg) . "\n";
                flush();
                continue;
            }

            $enrolplugin = enrol_get_plugin('manual');
            $instances = enrol_get_instances($courseid, true);
            $manualinstance = null;

            // Procurar a Instancia no AMB
            foreach ($instances as $instance) {
                if ($instance->enrol == 'manual') {
                    $manualinstance = $instance;
                    break;
                }
            }

            if ($manualinstance) {
                $context = context_course::instance($courseid);
                $enrolplugin->enrol_user($manualinstance, $userid, 5, time(),0,$data['SITUACAO']);
                $msg = "Usuário ID: {$userid} matriculado com sucesso no curso ID: {$courseid}.";

                // Se foi matriculado por IDMODELO, inserir na tabela de controle
                if (!empty($data['IDMODELO']) && $data['IDMODELO'] !== null) {
                    $status_control = ($data['SITUACAO'] == 0) ? 1 : 0; // 1 para matriculado, 0 para suspenso
                    $control_result = sge_insert_matricula_control(
                        $userid,
                        $courseid,
                        $data['IDTURMADISC'],
                        $data['CODFILIAL'],
                        $status_control
                    );

                    if ($control_result) {
                        $msg .= " Registro de controle inserido com sucesso.";
                    } else {
                        $msg .= " Erro ao inserir registro de controle.";
                    }
                }
            } else {
                $msg = "Erro: instância de inscrição manual não encontrada para o curso ID: {$courseid}.";
            }

            echo html_writer::tag('pre', $msg) . "\n";
            flush();

        } else {
            $courseid = $data['IDCTM'];
            $userid = sgeintegracao_get_user_CTM($data['USUARIO']);
            $statusenrol = sgeintegracao_is_user_enrolled($userid[0]['id'], $courseid);

            if ($statusenrol['message']) {
                $msg = 'Erro ao consultar API: ' . $statusenrol['message'] . '| DEBUG: ' . $statusenrol['debuginfo'];
                echo html_writer::tag('pre', $msg) . "\n";
                flush();
                continue;
            }

            if ($statusenrol === TRUE) {
                sgeintegracao_enrol_user_CTM($userid[0]['id'], $courseid,$data['SITUACAO'], $data);
                sgeintegracao_group_CTM($userid[0]['id'], $courseid, $data);
                flush();
                continue;
            } else {
                $enroluser = sgeintegracao_enrol_user_CTM($userid[0]['id'], $courseid,$data['SITUACAO'], $data);
                sgeintegracao_group_CTM($userid[0]['id'], $courseid, $data);

                if (!$enroluser['message']) {
                    $msg = 'Usuário CTM com CPF ' . $userid[0]['username'] . ' Cadastrado no Curso AMB CTM ID: ' . $courseid;
                } else {
                    $msg = 'Erro ao matricular o Usuário CTM: ' . $enroluser['message'] . '| DEBUG: ' . $enroluser['debuginfo'];
                }

                echo html_writer::tag('pre', $msg) . "\n";
                flush();
            }
        }
    }

    $progress->end_progress();
}